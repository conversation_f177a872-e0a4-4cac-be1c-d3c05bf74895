// Improved Etsy-specific product data extractor

(function() {
  'use strict';

  // Ensure CommonExtractor is available
  if (typeof window.CommonExtractor === 'undefined') {
    console.error('CommonExtractor not found! Please inject CommonExtractor first.');
    return;
  }

  const EtsyExtractor = {
    // Enhanced Etsy-specific selectors
    selectors: {
      title: [
        'h1[data-test-id="listing-page-title"]',
        'h1[data-testid="listing-page-title"]',
        'h1.listing-page-title',
        '[data-test-id="listing-page-title"] h1',
        '[data-testid="listing-page-title"] h1',
        '.listing-page-title h1',
        'h1[class*="listing"]',
        'h1[class*="title"]'
      ],
      images: [
        // Specific Etsy main product image selectors (prioritized)
        'img[data-testid="listing-page-image"]',
        'img[data-test-id="listing-page-image"]',
        '[data-testid="listing-page-image-carousel"] img[src*="etsystatic"]',
        '[data-test-id="listing-page-image-carousel"] img[src*="etsystatic"]',
        '.listing-page-image-carousel img[src*="etsystatic"]',

        // Container-based selectors for main product images only
        '.listing-page-image img[src*="etsystatic"]',
        '.image-carousel img[src*="etsystatic"]',
        '.listing-image img[src*="etsystatic"]',
        '.product-image img[src*="etsystatic"]',

        // Gallery selectors for main product images only
        '.image-gallery img[src*="etsystatic"]',
        '.photo-gallery img[src*="etsystatic"]',
        '.listing-gallery img[src*="etsystatic"]',

        // Fallback selectors (more restrictive)
        'img[src*="etsystatic"][src*="il_1140xN"]',
        'img[src*="etsystatic"][src*="il_570xN"]',
        'img[src*="etsystatic"][src*="il_fullxfull"]'
      ],
      seller: [
        'a[data-testid="shop-name-link"]',
        'a[data-test-id="shop-name-link"]',
        '.shop-name a',
        '.shop-info a',
        '[data-testid="shop-name"] a',
        '[data-test-id="shop-name"] a'
      ],
      price: [
        '.currency-value',
        '.currency-symbol + .currency-value',
        '[data-testid="price"] .currency-value',
        '[data-test-id="price"] .currency-value',
        '.notranslate',
        '[class*="currency-value"]',
        '[data-testid="price"]',
        '[data-test-id="price"]',
        '.price .currency-value',
        '.listing-price'
      ],
      rating: [
        '[data-testid="reviews-summary"] [data-testid="rating"]',
        '[data-test-id="reviews-summary"] [data-test-id="rating"]',
        '.shop-rating .rating',
        '.stars-rating',
        '[class*="rating"]',
        '.rating-stars'
      ],
      reviewCount: [
        '[data-testid="reviews-summary"] a',
        '[data-test-id="reviews-summary"] a',
        '.review-count',
        '.reviews-count',
        '[data-testid="review-count"]',
        '[data-test-id="review-count"]'
      ],
      description: [
        '[data-testid="description-text"]',
        '[data-test-id="description-text"]',
        '.listing-page-description',
        '.description-text',
        '.listing-description',
        '[class*="description"]'
      ],
      availability: [
        '[data-testid="quantity-select"]',
        '[data-test-id="quantity-select"]',
        '.quantity-select',
        '.inventory-quantity',
        '[name="quantity"]'
      ]
    },

    // Extract product data from Etsy listing page
    async extractProductData() {
      try {
        CommonExtractor.log('Starting Etsy product data extraction...');

        // Wait a bit for dynamic content to load
        await this.waitForContent();

        const productData = {
          title: this.extractTitle(),
          productUrl: CommonExtractor.getCurrentUrl(),
          marketplace: 'etsy',
          sellerName: this.extractSeller(),
          images: this.extractImages(),
          metadata: this.extractMetadata()
        };

        CommonExtractor.log('Extracted product data:', productData);

        // Validate the extracted data
        const validation = CommonExtractor.validateProductData(productData);

        if (!validation.isValid) {
          CommonExtractor.log('Validation failed:', validation.errors);
          CommonExtractor.showNotification(
            'Failed to extract complete product data: ' + validation.errors.join(', '),
            'error'
          );
          return null;
        }

        CommonExtractor.log('Etsy product data extracted successfully');
        CommonExtractor.showNotification('Product data extracted successfully!', 'success');
        return productData;

      } catch (error) {
        CommonExtractor.log('Error extracting Etsy product data:', error);
        CommonExtractor.showNotification('Error extracting product data: ' + error.message, 'error');
        return null;
      }
    },

    // Wait for content to load
    async waitForContent() {
      try {
        // Wait for either title or images to be available
        await Promise.race([
          CommonExtractor.waitForElement(this.selectors.title[0], 3000),
          CommonExtractor.waitForElement(this.selectors.images[0], 3000)
        ]);

        // Additional wait for dynamic content
        await new Promise(resolve => setTimeout(resolve, 1000));
      } catch (error) {
        CommonExtractor.log('Content wait timeout, proceeding anyway');
      }
    },

    extractTitle() {
      CommonExtractor.log('Extracting title...');

      for (const selector of this.selectors.title) {
        const title = CommonExtractor.extractText(selector);
        if (title && title.length > 3) {
          CommonExtractor.log(`Title found with selector: ${selector}`);
          return CommonExtractor.cleanText(title);
        }
      }

      // Fallback to page title
      const pageTitle = document.title;
      if (pageTitle && !pageTitle.toLowerCase().includes('etsy') && pageTitle.length > 3) {
        const cleanTitle = pageTitle.split('|')[0].split('-')[0].trim();
        if (cleanTitle.length > 3) {
          CommonExtractor.log('Using page title as fallback');
          return CommonExtractor.cleanText(cleanTitle);
        }
      }

      // Last resort - try any h1
      const h1Elements = document.querySelectorAll('h1');
      for (const h1 of h1Elements) {
        const text = CommonExtractor.cleanText(h1.textContent);
        if (text && text.length > 3 && !text.toLowerCase().includes('etsy')) {
          CommonExtractor.log('Using h1 as last resort');
          return text;
        }
      }

      throw new Error('Could not extract product title');
    },

    extractSeller() {
      CommonExtractor.log('Extracting seller...');

      for (const selector of this.selectors.seller) {
        const seller = CommonExtractor.extractText(selector);
        if (seller && seller.length > 1) {
          CommonExtractor.log(`Seller found: ${seller}`);
          return CommonExtractor.cleanText(seller);
        }
      }

      CommonExtractor.log('No seller found');
      return null;
    },

    extractImages() {
      CommonExtractor.log('Extracting images...');

      // First try to extract main product gallery images specifically
      let images = this.extractMainProductImages();

      // If no images found, try the standard extraction with more specific filtering
      if (images.length === 0) {
        CommonExtractor.log('No main product images found, trying standard selectors...');
        images = CommonExtractor.extractImages(this.selectors.images);
        images = this.filterProductGalleryImages(images);
      }

      // If still no images found, try more aggressive search but with strict filtering
      if (images.length === 0) {
        CommonExtractor.log('No images with standard selectors, trying filtered aggressive search...');
        images = this.aggressiveImageSearch();
      }

      // Process Etsy-specific image URLs and ensure high resolution
      images = this.processEtsyImages(images);

      CommonExtractor.log(`Found ${images.length} main product images`);
      return images;
    },

    // Extract main product gallery images specifically
    extractMainProductImages() {
      const images = [];
      const seenUrls = new Set();

      // Try to find the main product image carousel/gallery
      const gallerySelectors = [
        '[data-testid="listing-page-image-carousel"] img',
        '[data-test-id="listing-page-image-carousel"] img',
        '.listing-page-image-carousel img',
        '.image-carousel img[src*="etsystatic"]',
        '.listing-image img[src*="etsystatic"]',
        'img[data-testid="listing-page-image"]',
        'img[data-test-id="listing-page-image"]'
      ];

      for (const selector of gallerySelectors) {
        const galleryImages = document.querySelectorAll(selector);

        galleryImages.forEach((img) => {
          let imageUrl = img.src || img.getAttribute('data-src') || img.getAttribute('data-lazy-src');

          if (imageUrl && this.isMainProductImage(imageUrl)) {
            imageUrl = CommonExtractor.normalizeUrl(imageUrl);

            if (!seenUrls.has(imageUrl)) {
              seenUrls.add(imageUrl);
              images.push({
                imageUrl: imageUrl,
                isPrimary: images.length === 0,
                sortOrder: images.length
              });
            }
          }
        });

        // If we found images with this selector, stop trying others
        if (images.length > 0) {
          CommonExtractor.log(`Found ${images.length} main product images using selector: ${selector}`);
          break;
        }
      }

      return images;
    },

    // Filter images to only include main product gallery images
    filterProductGalleryImages(images) {
      return images.filter(image => this.isMainProductImage(image.imageUrl));
    },

    // Check if an image URL is a main product image (not thumbnail, avatar, etc.)
    isMainProductImage(url) {
      // Must be from Etsy static CDN
      if (!url.includes('etsystatic.com')) {
        return false;
      }

      // Main product images follow specific URL patterns
      const mainImagePatterns = [
        /\/il_\d+x\d+\./,     // Standard size format like il_1140xN, il_570xN
        /\/il_fullxfull\./,   // Full size images
        /\/il_\d+xN\./        // Variable height format like il_1140xN
      ];

      const hasMainPattern = mainImagePatterns.some(pattern => pattern.test(url));
      if (!hasMainPattern) {
        return false;
      }

      // Exclude thumbnail and small images
      const excludePatterns = [
        /\/il_170x135\./,     // Small thumbnails
        /\/il_75x75\./,       // Tiny thumbnails
        /\/il_340x270\./,     // Medium thumbnails
        /\/il_300x300\./,     // Square thumbnails
        /\/il_430xN\./,       // Smaller preview images
        /avatar/i,            // User avatars
        /shop.*icon/i,        // Shop icons
        /badge/i,             // Badges
        /logo/i               // Logos
      ];

      const hasExcludePattern = excludePatterns.some(pattern => pattern.test(url));
      if (hasExcludePattern) {
        return false;
      }

      return true;
    },

    // More aggressive image search when standard selectors fail
    aggressiveImageSearch() {
      const images = [];
      const seenUrls = new Set();

      // Find all images on the page, but only look for main product images
      const allImages = document.querySelectorAll('img[src*="etsystatic"]');

      allImages.forEach((img) => {
        let imageUrl = img.src || img.getAttribute('data-src') || img.getAttribute('data-lazy-src');

        if (imageUrl && this.isMainProductImage(imageUrl)) {
          imageUrl = CommonExtractor.normalizeUrl(imageUrl);

          if (!seenUrls.has(imageUrl)) {
            seenUrls.add(imageUrl);
            images.push({
              imageUrl: imageUrl,
              isPrimary: images.length === 0,
              sortOrder: images.length
            });
          }
        }
      });

      return images;
    },

    // Check if an image is likely a product image
    isProductImage(img, url) {
      // Must be from Etsy
      if (!url.includes('etsy') && !url.includes('etsystatic')) {
        return false;
      }

      // Must be reasonably sized
      if (img.width < 50 || img.height < 50) {
        return false;
      }

      // Exclude certain types
      const excludePatterns = [
        'avatar', 'logo', 'icon', 'badge', 'button',
        'header', 'footer', 'nav', 'menu'
      ];

      const lowerUrl = url.toLowerCase();
      const lowerAlt = (img.alt || '').toLowerCase();
      const lowerClass = (img.className || '').toLowerCase();

      for (const pattern of excludePatterns) {
        if (lowerUrl.includes(pattern) || lowerAlt.includes(pattern) || lowerClass.includes(pattern)) {
          return false;
        }
      }

      return true;
    },

    // Process Etsy-specific image URLs
    processEtsyImages(images) {
      return images.map((image, index) => {
        let imageUrl = image.imageUrl;

        // Convert Etsy thumbnail URLs to high-resolution il_1140xN format
        const sizeReplacements = [
          ['il_170x135', 'il_1140xN'],     // Small thumbnails to high-res
          ['il_340x270', 'il_1140xN'],     // Medium thumbnails to high-res
          ['il_300x300', 'il_1140xN'],     // Square thumbnails to high-res
          ['il_570xN', 'il_1140xN'],       // Medium size to high-res
          ['il_794xN', 'il_1140xN'],       // Common medium-high size to high-res
          ['il_430xN', 'il_1140xN'],       // Smaller preview to high-res
          ['il_75x75', 'il_1140xN'],       // Tiny thumbnails to high-res
          ['il_fullxfull', 'il_1140xN']    // Full size to standardized high-res
        ];

        for (const [oldSize, newSize] of sizeReplacements) {
          if (imageUrl.includes(oldSize)) {
            imageUrl = imageUrl.replace(oldSize, newSize);
            break;
          }
        }

        // If no size replacement was made but it's an etsystatic URL,
        // use regex to replace any il_XXXxXXX or il_XXXxN pattern with il_1140xN
        if (imageUrl.includes('etsystatic.com') && !imageUrl.includes('il_1140xN')) {
          // Replace any il_XXXxXXX or il_XXXxN pattern with il_1140xN
          imageUrl = imageUrl.replace(/il_\d+x(?:\d+|N)/g, 'il_1140xN');
        }

        return {
          ...image,
          imageUrl: imageUrl,
          isPrimary: index === 0,
          sortOrder: index
        };
      });
    },

    extractMetadata() {
      CommonExtractor.log('Extracting metadata...');

      const metadata = {};

      // Extract price
      const price = this.extractPrice();
      if (price) {
        metadata.price = price.raw;
        metadata.currency = price.currency;
        metadata.amount = price.amount;
      }

      // Extract rating
      const rating = this.extractRating();
      if (rating) {
        metadata.rating = rating;
      }

      // Extract review count
      const reviewCount = this.extractReviewCount();
      if (reviewCount) {
        metadata.reviewCount = reviewCount;
      }

      // Extract description
      const description = this.extractDescription();
      if (description) {
        metadata.description = description;
      }

      // Extract availability
      const availability = this.extractAvailability();
      if (availability) {
        metadata.availability = availability;
      }

      CommonExtractor.log('Extracted metadata:', metadata);
      return metadata;
    },

    extractPrice() {
      return CommonExtractor.extractPrice(this.selectors.price);
    },

    extractRating() {
      for (const selector of this.selectors.rating) {
        const rating = CommonExtractor.extractRating([selector]);
        if (rating) {
          return rating;
        }
      }

      return null;
    },

    extractReviewCount() {
      for (const selector of this.selectors.reviewCount) {
        try {
          const element = document.querySelector(selector);
          if (element) {
            const text = element.textContent.trim();
            const match = text.match(/(\d+(?:,\d+)*)/);
            if (match) {
              return match[1];
            }
          }
        } catch (error) {
          CommonExtractor.log(`Failed to extract review count from ${selector}:`, error);
        }
      }

      return null;
    },

    extractDescription() {
      for (const selector of this.selectors.description) {
        const description = CommonExtractor.extractText(selector);
        if (description && description.length > 10) {
          return CommonExtractor.cleanText(description).substring(0, 500);
        }
      }

      return null;
    },

    extractAvailability() {
      for (const selector of this.selectors.availability) {
        try {
          const element = document.querySelector(selector);
          if (element) {
            // Check if quantity selector exists and is not disabled
            if (element.tagName === 'SELECT' && !element.disabled) {
              return 'In Stock';
            }

            const text = element.textContent.trim().toLowerCase();
            if (text.includes('in stock') || text.includes('available')) {
              return 'In Stock';
            } else if (text.includes('out of stock') || text.includes('sold out')) {
              return 'Out of Stock';
            }
          }
        } catch (error) {
          CommonExtractor.log(`Failed to extract availability from ${selector}:`, error);
        }
      }

      return 'Unknown';
    },

    // Check if current page is an Etsy product page
    isProductPage() {
      return window.location.pathname.includes('/listing/') &&
             window.location.hostname.includes('etsy.com');
    },

    // Manual extraction trigger
    async extract() {
      if (!this.isProductPage()) {
        CommonExtractor.showNotification('This is not an Etsy product page!', 'error');
        return null;
      }

      CommonExtractor.showNotification('Extracting product data...', 'info');
      return await this.extractProductData();
    }
  };

  // Make it globally available
  window.EtsyExtractor = EtsyExtractor;

  // Auto-extract when page loads (for manual crawling)
  if (EtsyExtractor.isProductPage()) {
    // Wait for page to fully load
    if (document.readyState === 'loading') {
      document.addEventListener('DOMContentLoaded', () => {
        setTimeout(() => {
          CommonExtractor.log('Etsy product page detected and ready for extraction');
        }, 1000);
      });
    } else {
      CommonExtractor.log('Etsy product page detected and ready for extraction');
    }
  }

  console.log('Improved EtsyExtractor loaded successfully!');
})();